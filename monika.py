import json
import logging
import time
import winsound
import re
from pathlib import Path
from wxauto import WeChat

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

class WeChatMonitor:
    def __init__(self, config_path="config.json"):
        self.config = self.load_config(config_path)
        self.wx = WeChat()
        self.processed_messages = set()  # 用于存储已处理的消息ID
        self.last_cleanup_time = time.time()  # 记录上次清理时间
        self.last_message_id = None  # 记录最后处理的消息ID
        
        # 初始化日志
        if self.config.get("debug", False):
            logger.setLevel(logging.DEBUG)
        
        logger.info("微信监控器初始化完成")
    
    def parse_time_interval(self, interval):
        """解析时间间隔，支持秒(s)、分钟(m)、小时(h)"""
        if isinstance(interval, (int, float)):
            return interval
        
        if isinstance(interval, str):
            match = re.match(r'^(\d+)([smh])?$', interval.lower())
            if match:
                value = int(match.group(1))
                unit = match.group(2) or 's'
                
                if unit == 's':
                    return value
                elif unit == 'm':
                    return value * 60
                elif unit == 'h':
                    return value * 3600
        
        # 默认值
        return 2
    
    def load_config(self, config_path):
        """加载配置文件"""
        default_config = {
            "monitor_chats": [],  # 要监控的群聊或联系人列表
            "blocked_users": [],  # 要屏蔽的用户列表
            "blocked_keywords": [],  # 要屏蔽的关键词列表
            "check_interval": 2,  # 检查间隔(秒)
            "sound_alert": True,  # 是否启用声音提醒
            "enable_cleanup": True,  # 是否启用清理机制
            "cleanup_interval": "1h",  # 清理间隔
            "debug": False  # 调试模式
        }
        
        config_file = Path(config_path)
        if not config_file.exists():
            # 创建默认配置文件
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, ensure_ascii=False, indent=4)
            logger.warning(f"配置文件不存在，已创建默认配置: {config_path}")
            return default_config
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 合并配置，确保所有必要的键都存在
            for key, value in default_config.items():
                if key not in config:
                    config[key] = value
            
            # 解析时间间隔
            config["check_interval"] = self.parse_time_interval(config.get("check_interval", 2))
            config["cleanup_interval"] = self.parse_time_interval(config.get("cleanup_interval", "1h"))
            
            logger.info("配置文件加载成功")
            return config
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return default_config
    
    def is_message_blocked(self, message, sender):
        """检查消息是否应该被屏蔽"""
        # 检查发送者是否在屏蔽列表中
        if sender in self.config["blocked_users"]:
            logger.debug(f"消息被屏蔽（发送者: {sender}）")
            return True
        
        # 检查是否包含屏蔽关键词
        for keyword in self.config["blocked_keywords"]:
            if keyword in message:
                logger.debug(f"消息被屏蔽（关键词: {keyword}）")
                return True
        
        return False
    
    def get_message_id(self, message_info):
        """生成消息的唯一ID（基于消息自身属性）"""
        try:
            # 使用消息自身的时间、发送者和内容创建唯一ID
            # 避免使用当前时间，改用消息自带的时间属性
            timestamp = message_info.get("msg_time", int(time.time() * 1000))
            # 结合多种属性确保唯一性
            unique_str = f"{message_info.get('chat', '')}_{message_info.get('sender', '')}_{message_info.get('message', '')}_{timestamp}"
            return hash(unique_str)
        except Exception as e:
            logger.debug(f"生成消息ID失败: {e}")
            #  fallback方案
            return hash(f"{message_info.get('sender', '')}_{message_info.get('message', '')}_{int(time.time() * 1000)}")
    
    def play_alert_sound(self):
        """播放提醒声音"""
        if self.config["sound_alert"]:
            try:
                winsound.Beep(1000, 500)  # 频率1000Hz，持续时间500ms
            except Exception as e:
                logger.error(f"播放声音失败: {e}")
    
    def cleanup_processed_messages(self):
        """清理已处理的消息集合"""
        if not self.config["enable_cleanup"]:
            return
        
        current_time = time.time()
        if current_time - self.last_cleanup_time >= self.config["cleanup_interval"]:
            before_count = len(self.processed_messages)
            self.processed_messages.clear()
            after_count = len(self.processed_messages)
            self.last_cleanup_time = current_time
            logger.info(f"已清理已处理消息集合: {before_count} -> {after_count}")
    
    def monitor(self):
        """开始监控消息"""
        logger.info("开始监控微信消息...")
        logger.info(f"监控的聊天: {self.config['monitor_chats']}")
        logger.info(f"屏蔽的用户: {self.config['blocked_users']}")
        logger.info(f"屏蔽的关键词: {self.config['blocked_keywords']}")
        logger.info(f"检查间隔: {self.config['check_interval']}秒")
        
        if self.config["enable_cleanup"]:
            logger.info(f"清理机制已启用，清理间隔: {self.config['cleanup_interval']}秒")
        else:
            logger.info("清理机制未启用")
        
        try:  
            while True:  
                # 获取所有消息，取最新的一条  
                all_messages = self.wx.GetAllMessage()  
                if all_messages and len(all_messages) > 0:  
                    # 取最新的一条消息  
                    last_msg = all_messages[-1]  
                      
                    # 解析消息信息  
                    sender = last_msg.sender  
                    message = last_msg.content  
                    msg_time = getattr(last_msg, 'time', int(time.time() * 1000))  # 消息发送时间
                      
                    # 获取聊天信息  
                    try:  
                        chat_info = self.wx.ChatInfo()  
                        chat_name = chat_info.get('chat_name', '未知聊天') if chat_info else '未知聊天'  
                    except:  
                        chat_name = '未知聊天'  
                      
                    # 生成消息ID  
                    message_info = {  
                        "chat": chat_name,  
                        "sender": sender,  
                        "message": message,  
                        "msg_time": msg_time  # 使用消息自身的时间
                    }  
                    message_id = self.get_message_id(message_info)  
                    
                    # 检查是否是新消息且未被处理  
                    if (message_id not in self.processed_messages and   
                        message_id != self.last_message_id and  # 额外检查是否与上一条相同
                        chat_name in self.config["monitor_chats"] and  
                        not self.is_message_blocked(message, sender)):  
                          
                        # 记录并提醒  
                        self.processed_messages.add(message_id)  
                        self.last_message_id = message_id  # 更新最后处理的消息ID
                        log_msg = f"新消息 - [{chat_name}] {sender}: {message}"  
                        logger.info(log_msg)  
                        print(f"\n{log_msg}")  
                        self.play_alert_sound()  
                  
                # 执行清理操作  
                self.cleanup_processed_messages()  
                  
                # 控制检查频率  
                time.sleep(self.config["check_interval"])  
                  
        except KeyboardInterrupt:  
            logger.info("监控被用户中断")  
        except Exception as e:  
            logger.error(f"监控过程中发生错误: {e}")  
            raise

if __name__ == "__main__":
    monitor = WeChatMonitor()
    monitor.monitor()
    